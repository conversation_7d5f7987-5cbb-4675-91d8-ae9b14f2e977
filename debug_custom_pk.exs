#!/usr/bin/env elixir

# Debug script to understand what's happening with custom_pk table

Mix.install([
  {:ecto, "~> 3.13"},
  {:ecto_sql, "~> 3.13"},
  {:ecto_sqlite3, "~> 0.17"}
])

# Start the application
Application.ensure_all_started(:ecto)

# Define a simple repo
defmodule DebugRepo do
  use Ecto.Repo,
    otp_app: :debug,
    adapter: Ecto.Adapters.SQLite3
end

# Configure the repo
Application.put_env(:debug, DebugRepo,
  database: "priv/repo/sqlite/drops_test.db",
  pool_size: 1
)

# Start the repo
{:ok, _} = DebugRepo.start_link()

# Test introspection
IO.puts("=== Testing custom_pk table introspection ===")

try do
  # Check if table exists
  result = DebugRepo.query!("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_pk'")
  IO.inspect(result.rows, label: "Table exists")

  # Get table info
  result = DebugRepo.query!("PRAGMA table_info(custom_pk)")
  IO.inspect(result.rows, label: "Table info")

  # Test our introspection
  columns = Drops.Relation.SQL.Introspector.introspect_table_columns(DebugRepo, "custom_pk")
  IO.inspect(columns, label: "Introspected columns")

  # Test schema inference
  schema = Drops.Relation.SQL.Inference.infer_from_table("custom_pk", DebugRepo)
  IO.inspect(schema.fields |> Enum.map(& &1.name), label: "Inferred field names")
  IO.inspect(schema.primary_key.fields |> Enum.map(& &1.name), label: "Primary key field names")

rescue
  e ->
    IO.puts("Error: #{inspect(e)}")
end
