defmodule Drops.Relation.Inference.SchemaFieldASTTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.Field
  alias Drops.Relation.Inference.SchemaFieldAST

  describe "SchemaFieldAST protocol for Field struct" do
    test "generates basic field AST" do
      field = Field.new(:email, :string, :string, :email)

      ast = SchemaFieldAST.to_field_ast(field)

      # Should generate: Ecto.Schema.field(:email, :string)
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:email, :string]} = ast
    end

    test "generates field AST with source option" do
      field = Field.new(:email_address, :string, :string, :email)

      ast = SchemaFieldAST.to_field_ast(field)

      # Should generate: Ecto.Schema.field(:email_address, :string, [source: :email])
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:email_address, :string, [source: :email]]} = ast
    end

    test "generates field AST with parameterized type" do
      field =
        Field.new(:status, :string, {Ecto.Enum, values: [:active, :inactive]}, :status)

      ast = SchemaFieldAST.to_field_ast(field)

      # Should generate: Ecto.Schema.field(:status, Ecto.Enum, [values: [:active, :inactive]])
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:status, Ecto.Enum, [values: [:active, :inactive]]]} = ast
    end

    test "generates field AST with parameterized type and source" do
      field =
        Field.new(
          :user_status,
          :string,
          {Ecto.Enum, values: [:active, :inactive]},
          :status
        )

      ast = SchemaFieldAST.to_field_ast(field)

      # Should generate: Ecto.Schema.field(:user_status, Ecto.Enum, [values: [:active, :inactive], source: :status])
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:user_status, Ecto.Enum, opts]} = ast

      assert Keyword.get(opts, :values) == [:active, :inactive]
      assert Keyword.get(opts, :source) == :status
    end

    test "to_attribute_ast returns nil for regular fields" do
      field = Field.new(:email, :string, :string, :email)

      result = SchemaFieldAST.to_attribute_ast(field)

      assert result == nil
    end

    test "to_attribute_ast generates primary key attribute for binary_id id field" do
      field = Field.new(:id, :binary, :binary_id, :id)

      result = SchemaFieldAST.to_attribute_ast(field)

      # Check the structure without worrying about exact metadata
      assert {:@, _, [{:primary_key, _, [tuple_ast]}]} = result
      assert {:{}, _, [:id, :binary_id, [autogenerate: true]]} = tuple_ast
    end

    test "to_attribute_ast generates primary key attribute for UUID id field" do
      field = Field.new(:id, :binary, Ecto.UUID, :id)

      result = SchemaFieldAST.to_attribute_ast(field)

      # Check the structure without worrying about exact metadata
      assert {:@, _, [{:primary_key, _, [tuple_ast]}]} = result

      assert {:{}, _, [:id, {:__aliases__, _, [:Ecto, :UUID]}, [autogenerate: true]]} =
               tuple_ast
    end

    test "to_attribute_ast generates foreign key type attribute for binary_id foreign key" do
      field = Field.new(:user_id, :binary, :binary_id, :user_id)

      result = SchemaFieldAST.to_attribute_ast(field)

      assert {:@, _, [{:foreign_key_type, _, [:binary_id]}]} = result
    end

    test "to_attribute_ast generates foreign key type attribute for UUID foreign key" do
      field = Field.new(:organization_id, :binary, Ecto.UUID, :organization_id)

      result = SchemaFieldAST.to_attribute_ast(field)

      assert {:@, _, [{:foreign_key_type, _, [:binary_id]}]} = result
    end
  end

  describe "Custom protocol implementation" do
    # Define a custom field type for testing
    defmodule CustomField do
      defstruct [:name, :type, :options, :custom_attribute]
    end

    # Implement the protocol for our custom field type
    defimpl SchemaFieldAST, for: CustomField do
      def to_field_ast(%CustomField{} = field) do
        to_field_ast_with_category(field, :regular)
      end

      def to_field_ast_with_category(
            %CustomField{name: name, type: type, options: options},
            _category
          ) do
        if options == [] do
          quote do
            Ecto.Schema.field(unquote(name), unquote(type))
          end
        else
          quote do
            Ecto.Schema.field(unquote(name), unquote(type), unquote(options))
          end
        end
      end

      def to_attribute_ast(%CustomField{custom_attribute: nil}), do: nil

      def to_attribute_ast(%CustomField{custom_attribute: attr}) do
        quote do
          @custom_attr unquote(attr)
        end
      end
    end

    test "custom field generates basic field AST" do
      custom_field = %CustomField{name: :custom_field, type: :string, options: []}

      ast = SchemaFieldAST.to_field_ast(custom_field)

      # Should generate: Ecto.Schema.field(:custom_field, :string)
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:custom_field, :string]} = ast
    end

    test "custom field generates field AST with options" do
      custom_field = %CustomField{
        name: :custom_field,
        type: :string,
        options: [default: "test"]
      }

      ast = SchemaFieldAST.to_field_ast(custom_field)

      # Should generate: Ecto.Schema.field(:custom_field, :string, [default: "test"])
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:custom_field, :string, [default: "test"]]} = ast
    end

    test "custom field generates attribute AST when custom_attribute is set" do
      custom_field = %CustomField{
        name: :custom_field,
        type: :string,
        options: [],
        custom_attribute: "test_value"
      }

      ast = SchemaFieldAST.to_attribute_ast(custom_field)

      # Should generate: @custom_attr "test_value"
      assert {:@, _, [{:custom_attr, _, ["test_value"]}]} = ast
    end

    test "custom field returns nil for attribute AST when custom_attribute is nil" do
      custom_field = %CustomField{
        name: :custom_field,
        type: :string,
        options: [],
        custom_attribute: nil
      }

      result = SchemaFieldAST.to_attribute_ast(custom_field)

      assert result == nil
    end
  end

  describe "Protocol integration with Inference module" do
    alias Drops.Relation.Inference.FieldCandidate
    alias Drops.Relation.Inference

    test "generate_field_definition_from_candidate uses protocol" do
      field = Field.new(:email, :string, :string, :email)
      candidate = FieldCandidate.new(field, :inferred, :regular)

      ast = Inference.generate_field_definition_from_candidate(candidate)

      # Should generate the same AST as the protocol
      expected_ast = SchemaFieldAST.to_field_ast(field)
      assert ast == expected_ast
    end

    test "generate_field_definition_from_candidate adds primary_key option for composite keys" do
      field = Field.new(:id, :integer, :id, :id)
      candidate = FieldCandidate.new(field, :inferred, :composite_primary_key)

      ast = Inference.generate_field_definition_from_candidate(candidate)

      # Should add primary_key: true option
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:id, :id, [primary_key: true]]} = ast
    end

    test "to_field_ast_with_category handles composite primary key correctly" do
      field = Field.new(:id, :integer, :id, :id)

      # Regular field should not have primary_key option
      regular_ast = SchemaFieldAST.to_field_ast_with_category(field, :regular)

      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _, [:id, :id]} =
               regular_ast

      # Composite primary key field should have primary_key: true option
      composite_ast =
        SchemaFieldAST.to_field_ast_with_category(field, :composite_primary_key)

      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:id, :id, [primary_key: true]]} = composite_ast
    end

    test "Inference module uses protocol for attribute generation" do
      alias Drops.Relation.Inference.FieldCandidates
      alias Drops.Relation.Schema.PrimaryKey

      # Create a binary_id primary key field
      field = Field.new(:id, :binary, :binary_id, :id)
      candidate = FieldCandidate.new(field, :inferred, :primary_key)

      # Create field candidates collection
      candidates = FieldCandidates.new([candidate])

      # Generate primary key attribute using Inference module
      primary_key = %PrimaryKey{fields: [field]}

      result =
        Inference.generate_primary_key_attribute_from_candidates(candidates, primary_key)

      # Should use the protocol's to_attribute_ast result
      assert {:@, _, [{:primary_key, _, [tuple_ast]}]} = result
      assert {:{}, _, [:id, :binary_id, [autogenerate: true]]} = tuple_ast
    end
  end

  describe "Advanced custom protocol implementation" do
    # Define a more complex custom field type that might need special handling
    defmodule AdvancedCustomField do
      defstruct [:name, :type, :constraints, :virtual?, :computed_from]
    end

    # Implement the protocol for advanced custom field type
    defimpl SchemaFieldAST, for: AdvancedCustomField do
      def to_field_ast(%AdvancedCustomField{} = field) do
        to_field_ast_with_category(field, :regular)
      end

      def to_field_ast_with_category(%AdvancedCustomField{virtual?: true}, _category) do
        # Virtual fields don't get field definitions
        nil
      end

      def to_field_ast_with_category(
            %AdvancedCustomField{
              name: name,
              type: type,
              constraints: constraints
            },
            _category
          ) do
        opts = build_field_options(constraints)

        if opts == [] do
          quote do
            Ecto.Schema.field(unquote(name), unquote(type))
          end
        else
          quote do
            Ecto.Schema.field(unquote(name), unquote(type), unquote(opts))
          end
        end
      end

      def to_attribute_ast(%AdvancedCustomField{computed_from: nil}), do: nil

      def to_attribute_ast(%AdvancedCustomField{computed_from: fields})
          when is_list(fields) do
        quote do
          @derive {Jason.Encoder, only: unquote(fields)}
        end
      end

      defp build_field_options(constraints) when is_list(constraints) do
        Enum.reduce(constraints, [], fn
          {:min_length, value}, acc -> Keyword.put(acc, :validate, {:length, min: value})
          {:max_length, value}, acc -> Keyword.put(acc, :validate, {:length, max: value})
          {:default, value}, acc -> Keyword.put(acc, :default, value)
          _, acc -> acc
        end)
      end

      defp build_field_options(_), do: []
    end

    test "advanced custom field handles virtual fields" do
      virtual_field = %AdvancedCustomField{
        name: :full_name,
        type: :string,
        virtual?: true,
        computed_from: [:first_name, :last_name]
      }

      # Virtual fields should not generate field AST
      assert SchemaFieldAST.to_field_ast(virtual_field) == nil

      # But they can generate attributes
      ast = SchemaFieldAST.to_attribute_ast(virtual_field)

      # Check the structure without worrying about exact metadata
      assert {:@, _,
              [
                {:derive, _,
                 [
                   {{:__aliases__, _, [:Jason, :Encoder]},
                    [only: [:first_name, :last_name]]}
                 ]}
              ]} = ast
    end

    test "advanced custom field handles constraints" do
      constrained_field = %AdvancedCustomField{
        name: :username,
        type: :string,
        constraints: [min_length: 3, max_length: 20, default: "guest"],
        virtual?: false
      }

      ast = SchemaFieldAST.to_field_ast(constrained_field)

      # Should generate field with constraint options
      assert {{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _,
              [:username, :string, opts]} = ast

      assert Keyword.has_key?(opts, :validate)
      assert Keyword.get(opts, :default) == "guest"
    end

    test "advanced custom field returns nil for attribute when no computed_from" do
      regular_field = %AdvancedCustomField{
        name: :email,
        type: :string,
        constraints: [],
        virtual?: false,
        computed_from: nil
      }

      result = SchemaFieldAST.to_attribute_ast(regular_field)
      assert result == nil
    end
  end
end
