{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"fields": [{"__struct__": "Field", "ecto_type": "id", "meta": {}, "name": "id", "source": "id", "type": "integer"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "false"}, "name": "email", "source": "email", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "first_name", "source": "first_name", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "last_name", "source": "last_name", "type": "string"}, {"__struct__": "Field", "ecto_type": "integer", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "age", "source": "age", "type": "integer"}, {"__struct__": "Field", "ecto_type": "integer", "meta": {"check_constraints": [], "default": "true", "is_nullable": "true"}, "name": "is_active", "source": "is_active", "type": "integer"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "profile_data", "source": "profile_data", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": "[]", "is_nullable": "true"}, "name": "tags", "source": "tags", "type": "string"}, {"__struct__": "Field", "ecto_type": "decimal", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "score", "source": "score", "type": "decimal"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "birth_date", "source": "birth_date", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "true"}, "name": "last_login_at", "source": "last_login_at", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "false"}, "name": "inserted_at", "source": "inserted_at", "type": "string"}, {"__struct__": "Field", "ecto_type": "string", "meta": {"check_constraints": [], "default": null, "is_nullable": "false"}, "name": "updated_at", "source": "updated_at", "type": "string"}], "foreign_keys": [], "indices": {"__struct__": "Indices", "indices": [{"__struct__": "Index", "fields": [{"__struct__": "Field", "ecto_type": "unknown", "meta": {}, "name": "is_active", "source": "is_active", "type": "unknown"}], "name": "users_is_active_index", "type": "btree", "unique": "false"}, {"__struct__": "Index", "fields": [{"__struct__": "Field", "ecto_type": "unknown", "meta": {}, "name": "last_name", "source": "last_name", "type": "unknown"}, {"__struct__": "Field", "ecto_type": "unknown", "meta": {}, "name": "first_name", "source": "first_name", "type": "unknown"}], "name": "users_last_name_first_name_index", "type": "btree", "unique": "false"}, {"__struct__": "Index", "fields": [{"__struct__": "Field", "ecto_type": "unknown", "meta": {}, "name": "email", "source": "email", "type": "unknown"}], "name": "users_email_index", "type": "btree", "unique": "true"}]}, "primary_key": {"__struct__": "<PERSON><PERSON><PERSON>", "fields": [{"__struct__": "Field", "ecto_type": "id", "meta": {}, "name": "id", "source": "id", "type": "integer"}]}, "source": "users", "virtual_fields": []}}