defmodule Drops.Relation.Inference.FieldCandidate do
  @moduledoc """
  Represents a field candidate during schema inference.

  This structure helps organize field information and determine where each field
  should be placed in the final schema (primary key attributes, excluded fields,
  regular field definitions, etc.).
  """

  alias Drops.Relation.Schema.Field

  defstruct [
    # The Field struct with all metadata
    :field,
    # :inferred | :custom | :merged
    :source,
    # :primary_key | :foreign_key | :regular | :timestamp | :excluded
    :category,
    # :attribute | :field_definition | :excluded | :timestamps_macro
    :placement
  ]

  @type t :: %__MODULE__{
          field: Field.t(),
          source: :inferred | :custom | :merged,
          category: :primary_key | :foreign_key | :regular | :timestamp | :excluded,
          placement: :attribute | :field_definition | :excluded | :timestamps_macro
        }

  @doc """
  Creates a new FieldCandidate from a Field struct.
  """
  @spec new(Field.t(), atom(), atom()) :: t()
  def new(%Field{} = field, source, category) do
    placement = determine_placement(field, category)

    %__MODULE__{
      field: field,
      source: source,
      category: category,
      placement: placement
    }
  end

  # Determines where a field should be placed in the final schema.
  @spec determine_placement(Field.t(), atom()) :: atom()
  defp determine_placement(%Field{name: name, ecto_type: ecto_type}, category) do
    case category do
      :primary_key ->
        cond do
          # Default Ecto primary key - exclude (Ecto adds automatically)
          name == :id and ecto_type == :id ->
            :excluded

          # Binary ID and UUID primary keys need only @primary_key attribute (Ecto handles the field)
          ecto_type in [:binary_id, Ecto.UUID] ->
            :attribute

          # Other custom primary keys need @primary_key attribute only
          true ->
            :attribute
        end

      :composite_primary_key ->
        # Composite primary key fields are defined as regular fields with primary_key: true
        :field_definition

      :timestamp ->
        # Timestamp fields use the timestamps() macro
        :timestamps_macro

      :excluded ->
        # Fields that should not appear in the schema
        :excluded

      _ ->
        # Regular fields and foreign keys are defined as field() calls
        :field_definition
    end
  end

  @doc """
  Categorizes a field based on its characteristics and context.
  """
  @spec categorize_field(Field.t(), [atom()], boolean(), boolean()) :: atom()
  def categorize_field(
        %Field{name: name},
        primary_key_names,
        is_association_fk,
        is_composite_pk \\ false
      ) do
    cond do
      name in primary_key_names and is_composite_pk ->
        :composite_primary_key

      name in primary_key_names ->
        :primary_key

      name in [:inserted_at, :updated_at] ->
        :timestamp

      is_association_fk ->
        :excluded

      foreign_key_field?(name) ->
        :foreign_key

      true ->
        :regular
    end
  end

  @doc """
  Checks if a field name looks like a foreign key field.
  """
  @spec foreign_key_field?(atom()) :: boolean()
  def foreign_key_field?(field_name) when is_atom(field_name) do
    field_name
    |> Atom.to_string()
    |> String.ends_with?("_id")
  end
end
